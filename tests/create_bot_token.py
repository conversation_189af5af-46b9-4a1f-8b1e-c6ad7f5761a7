#!/usr/bin/env python3
"""
<PERSON>ript to generate a long-lasting bot access token for automation purposes.
This creates a token valid for 365 days without going through the login endpoint.
"""

import sys
import argparse
from datetime import <PERSON><PERSON><PERSON>
import os
import json

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.security import create_access_token

def generate_bot_token(username, role, tenant_id, days=365):
    """
    Generate a long-lasting access token for a bot user.
    
    Args:
        username (str): The bot username
        role (str): The role for the bot (e.g., "admin", "agent")
        tenant_id (str): The tenant ID
        days (int): Number of days the token should be valid (default: 365)
        
    Returns:
        str: The generated JWT token
    """
    token_data = {
        "sub": username,
        "role": role,
        "tenant_id": tenant_id
    }
    
    access_token = create_access_token(
        data=token_data,
        expires_delta=timedelta(days=days)
    )
    
    return access_token

def main():
    parser = argparse.ArgumentParser(description="Generate a long-lasting bot access token")
    parser.add_argument("--username", required=True, help="Bot username")
    parser.add_argument("--role", required=True, help="Bot role (admin, agent, etc.)")
    parser.add_argument("--tenant-id", required=True, help="Tenant ID")
    parser.add_argument("--days", type=int, default=365, help="Token validity in days (default: 365)")
    parser.add_argument("--output", help="Output file to save the token (optional)")
    
    args = parser.parse_args()
    
    token = generate_bot_token(args.username, args.role, args.tenant_id, args.days)
    
    result = {
        "access_token": token,
        "token_type": "bearer",
        "username": args.username,
        "role": args.role,
        "tenant_id": args.tenant_id,
        "expires_in_days": args.days
    }
    
    # Print the token to stdout
    print(json.dumps(result, indent=2))
    
    # Save to file if specified
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(result, f, indent=2)
        print(f"\nToken saved to {args.output}")
    
    print("\nUse this token in the Authorization header as:")
    print(f"Authorization: Bearer {token}")

if __name__ == "__main__":
    main()