name: Deploy Docker Project to CentOS Server

on:
  push:
    branches:
      - master

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: SSH and Deploy with Docker
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_IP }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd /root/aroma-backend-v2

            # (Optional) Pull latest code from git if repo is cloned on the server
            git pull origin master

            # Build and run Docker containers
            #docker compose pull  # If using remote images
            docker compose up -d --build

