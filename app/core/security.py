# src/core/security.py

from datetime import datetime, timedelta
from argon2 import Password<PERSON>asher
from argon2.exceptions import VerifyMismatchError
# from passlib.context import CryptContext
from fastapi.security import OAuth2<PERSON>asswordBearer
from fastapi import Depends, HTTPException

import jwt
from app.core.config import SECRET_KEY, ALGORITHM
from app.models.user import UserTenantDB, User
from app.models.role import Role
from app.core.database import get_async_db_from_tenant_id, get_db_from_tenant_id
from typing import Optional


ph = PasswordHasher()
# pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")

def create_access_token(data: dict, expires_delta:timedelta = None):
    to_encode = data.copy() 
    if expires_delta:   
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=1)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_tenant_info(token: str = Depends(oauth2_scheme)) -> UserTenantDB:
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except jwt.PyJWTError:
        raise credentials_exception
    
    tenant_id = payload.get("tenant_id")
    tenant_db = get_async_db_from_tenant_id(tenant_id)
    sync_db = get_db_from_tenant_id(tenant_id)
    
    user = await tenant_db.users.find_one({"username": username})
    if user is None:
        raise credentials_exception

    role_collection = tenant_db.roles
    user_role = Role(**(await role_collection.find_one({"name": user["role"]})))
    user["role"] = user_role
    
    return UserTenantDB(
        tenant_id=tenant_id, 
        async_db=tenant_db, 
        db=sync_db, 
        user=User(**user)
    )


def create_invitation_token(username: str, role: str, invited_by: str, tenant_id:str, expires_delta: Optional[timedelta] = None):
    """
    Creates a JWT token for agent invitation.
    """
    to_encode = {"username": username, "invited_by": invited_by, "role": role, "tenant_id": tenant_id}
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=7)  # Default expiration: 7 days
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_invitation_token(token: str):
    """
    Verifies the invitation token and extracts the agent's name.
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("username")
        invited_by: str = payload.get("invited_by")
        role: str = payload.get("role")
        result = get_async_db_from_tenant_id(payload.get("tenant_id")).invitations.find_one({"username": username, "role": role})

        if username is None or invited_by is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        if result is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")
        
        return username, invited_by, role
    
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=400, detail="Invitation token has expired")
    except jwt.PyJWTError:
        raise HTTPException(status_code=400, detail="Invalid invitation token")


# Replace CryptContext with PasswordHasher
def hash_password(password: str) -> str:
    """
    Hash new passwords using Argon2
    """
    return ph.hash(password)
    
# Update the verify_password function
def verify_password(plain_password, hashed_password):
    """
    Verifies a plain password against a hashed password.
    """
    try:
        return ph.verify(hashed_password, plain_password)
    except VerifyMismatchError:
        return False


def require_roles(required_roles: list[str]):
    """
    Dependency that checks if the user has the required role.
    Usage: @router.get("/endpoint", dependencies=[Depends(require_role("admin"))])
    """
    async def check_role(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
        if user_tenant_info.user.role.name not in required_roles:
            raise HTTPException(
                status_code=403,
                detail=f"User does not have the required role: {', '.join(required_roles)}"
            )
        
        return user_tenant_info
    return check_role

