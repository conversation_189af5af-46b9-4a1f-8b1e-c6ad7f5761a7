import google.generativeai as genai

def gemini_model(system_instructions=None, model_name=None):
    if model_name is None:
        model_name = "gemini-1.5-flash"

    if system_instructions is None:
        system_instructions = "Extract the information from the image in devanagari format"

    generation_config = {
        "temperature": 1,
        "top_p": 0.95,
        "top_k": 64,
        "max_output_tokens": 8192,
        "response_mime_type": "application/json",
    }

    model = genai.GenerativeModel(
        model_name=model_name,
        generation_config=generation_config,
        system_instruction=system_instructions,
    )

    return model