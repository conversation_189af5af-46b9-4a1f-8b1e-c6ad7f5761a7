from fastapi import APIRouter, Depends, HTTPException, Query, Body
from bson import ObjectId
from datetime import datetime
from fastapi.responses import JSONResponse
from typing import Literal

from app.core.security import require_roles
from app.models.user import UserTenantDB
from app.core.helper import logger
from app.models.role import Role
from app.v1.schema.pagination import PaginationResponse

# Define the fixed roles
VALID_ROLES = ["admin", "supervisor", "agent"]

loggers = logger.setup_new_logging(__name__)
router = APIRouter(tags=["Roles"])

@router.get("/roles/")
async def read_roles(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> PaginationResponse[Role]:
    """Get all roles"""
    roles_collection = user_tenant.async_db.roles

    skip = (page - 1) * limit

    # Get total count
    total = await roles_collection.count_documents({})

    # Get paginated data
    roles = (
        await roles_collection.find().skip(skip).limit(limit).to_list(length=None)
    )

    return {
        "data": roles,
        "meta": {
            "page": page,
            "limit": limit,
            "total": total,
            "total_pages": (total + limit - 1) // limit,
        },
    }
from app.models.role import RoleUpdateRequest
from fastapi import Body
from bson import ObjectId
from datetime import datetime, timezone

@router.put("/update_roles")
async def update_user_role(
    payload: RoleUpdateRequest,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    """
    Update a user's role to one of the fixed roles: admin, supervisor, or agent.
    Only administrators can update roles.
    """
    users_collection = user_tenant_info.async_db.users

    # Verify user exists
    if not ObjectId.is_valid(payload.user_id):
        raise HTTPException(status_code=400, detail="Invalid user ID")

    user_id = ObjectId(payload.user_id)

    # Check if user exists
    user = await users_collection.find_one({"_id": user_id})
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Perform the update
    try:
        result = await users_collection.update_one(
            {"_id": user_id},
            {"$set": {
                "role": payload.role,
                "updated_at": datetime.now(timezone.utc),
                "updated_by": ObjectId(user_tenant_info.user.id)
            }}
        )

        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="User not found")

        if result.modified_count == 0:
            return {
                "message": f"User already has the role '{payload.role}', no changes made",
                "user_id": str(user_id),
                "role": payload.role
            }

        loggers.info(f"User {user_id} role updated to {payload.role} by {user_tenant_info.user.username}")

        return {
            "message": f"User role updated successfully to '{payload.role}'",
            "user_id": str(user_id),
            "role": payload.role
        }

    except Exception as e:
        loggers.error(f"Error updating user role: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
