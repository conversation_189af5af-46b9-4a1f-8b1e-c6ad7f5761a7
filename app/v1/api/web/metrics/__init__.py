from fastapi import APIRouter, Depends
from app.core.security import get_tenant_info, UserTenantDB
from app.core.helper.mongo_helper import serialize_mongo_doc

from pydantic import BaseModel, Field

from .job_metrics import jobs_metrics_router
from .recent_activity import activity_router

class RunPipeline(BaseModel):
    pipeline: list = Field(..., description="The pipeline to run")
    collection_name: str = Field(..., description="The collection name to run the pipeline on")

metrics_router = APIRouter(prefix="/metrics", tags=["Metrics"])
metrics_router.include_router(jobs_metrics_router)
metrics_router.include_router(activity_router)


@metrics_router.post("/pipeline/run")
async def run_custom_pipeline(request: RunPipeline, user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
    
    response = user_tenant_info.db[request.collection_name].aggregate(request.pipeline)
    response_list = list(response)
    # print(response_list)
    return serialize_mongo_doc(response_list)