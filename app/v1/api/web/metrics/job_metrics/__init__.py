from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, Query
from typing import Optional
from bson import ObjectId
from app.core.security import get_tenant_info, UserTenantDB
from app.core.helper.mongo_helper import serialize_mongo_doc
from app.v1.api.jobs.models import JobStatus

jobs_metrics_router = APIRouter()


@jobs_metrics_router.get("/job-count-by-status")
async def total_jobs_count(
    project_id: Optional[str] = Query(default=None),
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
):
    try:
        pipeline = []

        if project_id:
            if user_tenant_info.db.projects.find_one({"_id":ObjectId(project_id)}):
                pipeline.append({"$match": {"project_id": ObjectId(project_id)}})
            else:
                raise HTTPException(status_code=400, detail=f"Project not found for id - {project_id}")
           
        pipeline.extend([
            {"$group": {
                "_id": "$status",
                "count": {"$sum": 1}
            }},
            {"$project": {
                "_id": 0,
                "title": "$_id",
                "value": "$count"
            }}
        ])

        results = list(user_tenant_info.db.jobs.aggregate(pipeline))

        # Create a dictionary with all possible job statuses initialized to 0
        all_statuses = {status.value: 0 for status in JobStatus}

        # Update with actual counts from the database
        for result in results:
            status = result["title"]
            if status in all_statuses:
                all_statuses[status] = result["value"]

        # Convert back to the expected format
        status_results = [{"title": status, "value": count} for status, count in all_statuses.items()]

        total_jobs = sum(r["value"] for r in status_results)
        completed_jobs = next((r["value"] for r in status_results if r["title"].lower() == "completed"), 0)
        success_rate = round(completed_jobs / total_jobs, 2) if total_jobs else 0.0

        response = status_results + [
            {"title": "total_jobs", "value": total_jobs},
            {"title": "completed_jobs", "value": completed_jobs},
            {"title": "success_rate", "value": success_rate}
        ]

        return serialize_mongo_doc(response)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@jobs_metrics_router.get("/job-count-by-process")
async def total_jobs_count(
    project_id: Optional[str] = Query(default=None),
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
):
    try:
        pipeline = []

        if project_id:
            if user_tenant_info.db.projects.find_one({"_id":ObjectId(project_id)}):
                pipeline.append({"$match": {"project_id": ObjectId(project_id)}})
            else:
                raise HTTPException(status_code=400, detail=f"Project not found for id - {project_id}")

        pipeline.extend([
            {"$group": {
                "_id": "$process_name",
                "count": {"$sum": 1}
            }},
            {"$project": {
                "_id": 0,
                "title": "$_id",
                "value": "$count"
            }}
        ])

        results = list(user_tenant_info.db.jobs.aggregate(pipeline))

        # Get all available processes from the config collection
        processes_config = user_tenant_info.db.config.find_one({"name": "available_processes"})
        available_processes = processes_config.get("value", {}) if processes_config else {}

        # Create a dictionary with all available processes initialized to 0
        all_processes = {process_name: 0 for process_name in available_processes.keys()}

        # Update with actual counts from the database
        for result in results:
            process_name = result["title"]
            if process_name in all_processes:
                all_processes[process_name] = result["value"]

        # Convert back to the expected format
        process_results = [{"title": process_name, "value": count} for process_name, count in all_processes.items()]

        return serialize_mongo_doc(process_results)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

