from pydantic import BaseModel, computed_field
from typing import Optional
from datetime import datetime, timezone, timedelta

class JobActivity(BaseModel):
    id: str
    job_name: Optional[str] = None
    process_name: str
    status: str
    created_at: datetime # This is the crucial field
    created_by: str
    created_by_name: str
    completed_at: Optional[datetime] = None
    event: Optional[str] = None

    @computed_field
    @property
    def description(self) -> str:
        def time_ago(dt: datetime) -> str:
            # 1. Ensure dt is timezone-aware (preferably UTC)
            if dt.tzinfo is None:
                # Option A: Assume UTC if no timezone is provided (common for database retrieved values)
                dt = dt.replace(tzinfo=timezone.utc)
                # Option B: Raise an error or handle specifically if naive datetimes are not expected
                # raise ValueError("Input datetime 'dt' must be timezone-aware.")
            else:
                # 2. Convert dt to UTC if it's in a different timezone
                dt = dt.astimezone(timezone.utc)

            now = datetime.now(timezone.utc) # This is already UTC aware

            # Now both 'now' and 'dt' are timezone-aware and in UTC,
            # so subtraction will work correctly.
            diff = now - dt
            seconds = int(diff.total_seconds())
            minutes = seconds // 60
            hours = minutes // 60
            days = hours // 24
            months = days // 30 # Approximation
            years = days // 365 # Approximation

            if years > 0:
                return f"{years} year{'s' if years > 1 else ''} ago"
            if months > 0:
                return f"{months} month{'s' if months > 1 else ''} ago"
            if days > 0:
                return f"{days} day{'s' if days > 1 else ''} ago"
            if hours > 0:
                return f"{hours} hour{'s' if hours > 1 else ''} ago"
            if minutes > 0:
                return f"{minutes} minute{'s' if minutes > 1 else ''} ago"
            # Handle cases where the difference is very small (less than a minute)
            if seconds >= 0: # Ensure positive difference
                return "just now"
            else: # If dt is in the future
                return "in the future"

        job = self.job_name or self.process_name or "job"
        status_word = self.status or "created"
        user = self.created_by_name or "unknown user"
        ago = time_ago(self.created_at)
        return f"{job} {status_word}, by {user}. {ago}"

    class Config:
        from_attributes = True

