from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from typing import Optional
from datetime import datetime
from bson import ObjectId


class UserBase(BaseModel):
    """Base model for user data"""
    username: str
    role: str


class UserCreate(UserBase):
    """Model for creating a new user"""
    password: str

    @field_validator("username")
    def validate_username(cls, value):
        if len(value) < 3:
            raise ValueError("Username must be at least 3 characters long")
        return value

    @field_validator("password")
    def validate_password(cls, value):
        if len(value) < 6:
            raise ValueError("Password must be at least 8 characters long")

        return value


class UserUpdate(BaseModel):
    """Model for updating an existing user"""
    username: Optional[str] = None
    role: Optional[str] = None
    password: Optional[str] = None

    @field_validator("username")
    def validate_username(cls, value):
        if value is not None and len(value) < 3:
            raise ValueError("Username must be at least 3 characters long")
        return value

    @field_validator("password")
    def validate_password(cls, value):
        if value is not None and len(value) < 8:
            raise ValueError("Password must be at least 8 characters long")
        # Add more password validation rules as needed
        return value

    @model_validator(mode="after")
    def check_at_least_one_field(cls, values):
        if all(v is None for v in [values.username, values.role, values.password]):
            raise ValueError("At least one field must be provided for update")
        return values

class User(UserBase):
    """Model for user data returned to clients"""
    id: str = Field(alias="_id")
    created_at: Optional[datetime] = None
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str},
    )
