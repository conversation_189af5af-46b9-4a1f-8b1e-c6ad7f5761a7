from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from datetime import datetime
from app.core.security import get_tenant_info, require_roles
from app.models.user import UserTenantDB
from app.core.helper.mongo_helper import serialize_mongo_doc
from fastapi.responses import JSONResponse
from bson import ObjectId
from app.v1.schema.pagination import PaginationResponse

from .models import User

router = APIRouter(tags=["Users"])

@router.get("/users/{user_id}")
async def get_user_by_id(
    user_id: str,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    """
    Get user details by user ID (requires admin role)
    """
    try:
        # Get users collection from tenant database
        users_collection = user_tenant_info.async_db.users
        # Find user by ID
        user = await users_collection.find_one({"_id": ObjectId(user_id)}, {"username":1, "role":1})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Return only name and role information
        
        return serialize_mongo_doc(user)
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error fetching user details: {str(e)}")

@router.get("/users")
async def get_users(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search by username or email"),
    role: Optional[str] = Query(None, description="Filter by role"),
    created_at_start: Optional[datetime] = Query(None, description="Filter users created after this datetime"),
    created_at_end: Optional[datetime] = Query(None, description="Filter users created before this datetime"),
    sort_by_created_at: bool = Query(True, description="Sort by creation date"),
    sort_ascending: bool = Query(False, description="Sort in ascending order if True, descending if False"),
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
) -> PaginationResponse[User]:
    """
    Get users with pagination, filtering, and sorting options.

    Parameters:
    - page: Page number (starts at 1)
    - limit: Number of items per page (1-100)
    - search: Search by username or email
    - role: Filter by role
    - created_at_start: Filter users created after this datetime
    - created_at_end: Filter users created before this datetime
    - sort_by_created_at: Whether to sort by created_at field
    - sort_ascending: Sort in ascending order if True, descending if False
    """
    users_collection = user_tenant_info.async_db.users

    # Build the aggregation pipeline
    pipeline = []

    # Match stage for filtering
    match_query = {}

    if search:
        match_query["$or"] = [
            {"username": {"$regex": search, "$options": "i"}},
        ]

    if role:
        match_query["role"] = role

    if created_at_start or created_at_end:
        match_query["created_at"] = {}
        if created_at_start:
            match_query["created_at"]["$gte"] = created_at_start
        if created_at_end:
            # Add 23:59:59.999 to the end date to include the entire day
            end_of_day = created_at_end.replace(hour=23, minute=59, second=59, microsecond=999999)
            match_query["created_at"]["$lte"] = end_of_day

    if match_query:
        pipeline.append({"$match": match_query})

    # Sort stage
    if sort_by_created_at:
        sort_direction = 1 if sort_ascending else -1
        pipeline.append({"$sort": {"created_at": sort_direction}})

    # Count total documents for pagination
    count_pipeline = pipeline.copy()
    count_pipeline.append({"$count": "total"})

    # Get total count
    cursor = await users_collection.aggregate(count_pipeline)
    count_result = await cursor.to_list(length=1)
    total = count_result[0]["total"] if count_result else 0

    # Add pagination stages
    skip = (page - 1) * limit
    pipeline.append({"$skip": skip})
    pipeline.append({"$limit": limit})

    # Project stage to select fields
    pipeline.append({
        "$project": {
            "_id": 1,
            "username": 1,
            "role": 1,
            "created_at": 1
        }
    })

    # Execute the aggregation
    cursor = await users_collection.aggregate(pipeline)
    users = await cursor.to_list(length=None)

    try:
        return {
            "data": serialize_mongo_doc(users),
            "meta": {
                "page": page,
                "limit": limit,
                "total": total,
                "total_pages": (total + limit - 1) // limit,
            },
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching users: {str(e)}")