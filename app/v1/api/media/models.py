from pydantic import BaseModel, ConfigDict, Field, PlainSerializer, field_serializer
from typing import Annotated, Optional, Any, Dict
from datetime import datetime
from enum import Enum
from bson import ObjectId

class MediaType(str, Enum):
    IMAGE = "image"
    VIDEO = "video"
    DOCUMENT = "document"
    AUDIO = "audio"
    TEXT = "text"

class MediaBase(BaseModel):
    filename: str
    content_type: str
    description: Optional[str] = None
    bucket_name: str
    object_name: str

class MediaCreate(MediaBase):
    pass

class MediaUpdate(BaseModel):
    description: Optional[str] = None
    filename: Optional[str] = None

class Media(MediaBase):
    id: Annotated[ObjectId, PlainSerializer(str)] = Field(alias="_id")
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Annotated[ObjectId, PlainSerializer(str)]

    job_id: Annotated[ObjectId, PlainSerializer(str)]

    uploaded_at: Optional[datetime] = Field(default=None)
    uploaded_by: Optional[Annotated[ObjectId, PlainSerializer(str)]] = Field(default=None)
    size: Optional[int] = Field(default=None)
    metadata: Optional[Dict[str, Any]] = Field(default=None)

    @field_serializer('metadata')
    def serialize_metadata(self, metadata: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Custom serializer for metadata to handle ObjectId fields"""
        if metadata is None:
            return None

        # Create a copy to avoid modifying the original
        serialized_metadata = {}
        for key, value in metadata.items():
            if isinstance(value, ObjectId):
                serialized_metadata[key] = str(value)
            elif isinstance(value, dict):
                # Handle nested dictionaries that might contain ObjectIds
                serialized_metadata[key] = self._serialize_nested_dict(value)
            else:
                serialized_metadata[key] = value

        return serialized_metadata

    def _serialize_nested_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Helper method to serialize nested dictionaries containing ObjectIds"""
        serialized = {}
        for key, value in data.items():
            if isinstance(value, ObjectId):
                serialized[key] = str(value)
            elif isinstance(value, dict):
                serialized[key] = self._serialize_nested_dict(value)
            else:
                serialized[key] = value
        return serialized

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )
