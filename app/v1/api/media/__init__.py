from fastapi import APIRout<PERSON>, Depends, HTTPException
from app.core.security import get_tenant_info, require_roles
from app.models.user import UserTenantDB
from fastapi.responses import J<PERSON><PERSON>esponse
from bson import ObjectId
from datetime import timed<PERSON><PERSON>

from .models import Media

router = APIRouter(tags=["Media"], prefix="/media")

@router.get("/{media_id}")
async def get_media_details(
    media_id: str,
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
) -> Media:
    """ 
    Get details of a specific media file by ID

    Parameters:
    - media_id: ID of the media to retrieve
    - include_presigned_url: Whether to include a presigned URL for direct access
    - expiry: Expiry time in seconds for the presigned URL (if requested)
    """
    # Verify media exists
    if not ObjectId.is_valid(media_id):
        raise HTTPException(status_code=400, detail="Invalid media ID")

    # Get media collection from tenant database
    media_collection = user_tenant_info.async_db.media

    # Find media by ID
    media = await media_collection.find_one({"_id": ObjectId(media_id)})
    if not media:
        raise HTTPException(status_code=404, detail="Media not found")

    return media
