"""
Schema generation utility using Gemini API
"""

import json
import asyncio
import tempfile
import os
from typing import Dict, Any, Optional
import google.generativeai as genai
from app.models.user import UserTenantDB
from app.v1.processes.gemini_helpers import async_upload_to_gemini, async_generate_content, price_nep, get_model
from app.core.helper.logger import setup_new_logging

logger = setup_new_logging(__name__)


def _validate_schema_format(schema: Dict[str, Any]) -> bool:
    """
    Validate that the schema follows the required format with proper types and descriptions.
    """
    if not isinstance(schema, dict) or schema.get("type") != "object":
        return False

    properties = schema.get("properties", {})
    if not isinstance(properties, dict):
        return False

    # Check each property has required fields
    for prop_name, prop_def in properties.items():
        if not isinstance(prop_def, dict):
            return False

        # Must have type and description
        if "type" not in prop_def or "description" not in prop_def:
            return False

        # If it's an array, must have items
        if prop_def.get("type") == "array" and "items" not in prop_def:
            return False

        # Check for unsupported fields
        unsupported_fields = {"format", "pattern", "enum", "minLength", "maxLength", "minimum", "maximum"}
        if any(field in prop_def for field in unsupported_fields):
            return False

    return True


def _fix_schema_format(schema: Dict[str, Any]) -> Dict[str, Any]:
    """
    Fix common schema format issues to ensure compatibility.
    """
    if not isinstance(schema, dict):
        return schema

    # Ensure it's an object type
    if "type" not in schema:
        schema["type"] = "object"

    properties = schema.get("properties", {})
    if not isinstance(properties, dict):
        return schema

    # Fix each property
    for prop_name, prop_def in properties.items():
        if not isinstance(prop_def, dict):
            continue

        # Ensure type is present
        if "type" not in prop_def:
            prop_def["type"] = "string"  # Default to string

        # Ensure description is present
        if "description" not in prop_def:
            prop_def["description"] = f"Description for {prop_name}"

        # Fix array properties
        if prop_def.get("type") == "array" and "items" not in prop_def:
            prop_def["items"] = {"type": "string"}

        # Remove unsupported fields
        unsupported_fields = {"format", "pattern", "enum", "minLength", "maxLength", "minimum", "maximum"}
        for field in unsupported_fields:
            prop_def.pop(field, None)

    return schema


async def generate_schema_with_gemini(
    user_prompt: str,
    file_content: Optional[bytes] = None,
    file_name: Optional[str] = None,
    mime_type: Optional[str] = None,
    url: Optional[str] = None,
    user_tenant_info: UserTenantDB = None,
    generation_config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Generate a dynamic JSON schema using Gemini API based on user prompt and content.

    Args:
        user_prompt: User's description of what schema to generate
        file_content: Binary content of uploaded file (optional)
        file_name: Name of the uploaded file (optional)
        mime_type: MIME type of the content (optional)
        url: URL to analyze (optional)
        user_tenant_info: User tenant database information
        generation_config: Generation configuration parameters

    Returns:
        Dictionary containing the generated schema in the specified format
    """
    # Log function start with parameters
    logger.info(f"Starting schema generation - User: {user_tenant_info.user.id}, "
                f"Prompt length: {len(user_prompt)}, "
                f"Has file: {file_content is not None}, "
                f"Has URL: {url is not None}")

    if file_content:
        logger.info(f"File details - Name: {file_name}, Size: {len(file_content)} bytes, MIME: {mime_type}")

    try:
        # Get API key from config
        logger.debug("Retrieving Google API key from configuration")
        config = await user_tenant_info.async_db.config.find_one({"name": "api-keys"})
        if not config or "GOOGLE_API_KEY" not in config.get("value", {}):
            logger.error("Google API key not found in configuration")
            raise ValueError("Google API key not found in configuration")

        api_key = config["value"]["GOOGLE_API_KEY"]
        genai.configure(api_key=api_key)
        logger.info("Google API key retrieved and configured successfully")
        
        # Default generation config
        if generation_config is None:
            generation_config = {
                "temperature": 1,
                "top_p": 0.95,
                "top_k": 40,
                "max_output_tokens": 2048,
                "response_mime_type": "application/json"
            }
        logger.debug(f"Using generation config: {generation_config}")

        # Step 1: Generate system and user prompts based on user request
        logger.info("Step 1: Generating system and user prompts")
        prompt_generation_system = """You are an expert prompt engineer. Based on the user's request, generate appropriate system and user prompts for analyzing content and creating structured output schemas."""

        prompt_generation_user = f"""
        The user wants to: {user_prompt}

        Generate two prompts:
        1. A system prompt that defines the role and expertise needed
        2. A user prompt that gives specific instructions for analysis

        Return in this JSON format:
        {{
            "system_prompt": "Your system prompt here",
            "user_prompt": "Your user prompt here"
        }}
        """

        # Generate prompts using Gemini
        logger.debug("Creating prompt generation model")
        prompt_model = get_model(api_key, "gemini-2.0-flash", generation_config)

        # For prompt generation, we don't need file content, so use a different approach
        logger.info("Calling Gemini API for prompt generation")
        loop = asyncio.get_event_loop()
        def _generate_prompts():
            response = prompt_model.generate_content(f"{prompt_generation_system}\n\n{prompt_generation_user}")
            return response
        prompt_response = await loop.run_in_executor(None, _generate_prompts)

        logger.info(f"Prompt generation completed - Response length: {len(prompt_response.text)} chars")

        try:
            prompts_data = json.loads(prompt_response.text)
            system_prompt = prompts_data.get("system_prompt", "You are an expert content analyzer.")
            generated_user_prompt = prompts_data.get("user_prompt", user_prompt)
            logger.info("Successfully parsed generated prompts")
        except json.JSONDecodeError:
            logger.warning("Failed to parse prompt generation response, using defaults")
            system_prompt = "You are an expert content analyzer."
            generated_user_prompt = user_prompt
        
        # Step 2: Generate schema based on content and prompts
        logger.info("Step 2: Generating JSON schema based on content and prompts")
        schema_generation_system = f"""You are a JSON schema expert. {system_prompt}

Based on the content analysis, create a JSON schema that captures the structure needed for the user's request.

IMPORTANT: Follow this exact JSON structure format. Only use valid JSON schema fields:
- For objects: use "type": "object" with "properties"
- For arrays: use "type": "array" with "items"
- For strings: use "type": "string"
- For numbers: use "type": "number"
- For booleans: use "type": "boolean"
- Always include "description" for each property
- Do NOT use unsupported fields like "format", "pattern", "enum", etc.

Return a JSON object with this exact structure:
{{
    "schema": {{
        "type": "object",
        "properties": {{
            "PropertyName": {{
                "type": "string",
                "description": "Clear description of what this property represents"
            }},
            "ArrayProperty": {{
                "type": "array",
                "items": {{
                    "type": "string"
                }},
                "description": "Description of the array contents"
            }},
            "NumberProperty": {{
                "type": "number",
                "description": "Description of the numeric value"
            }}
        }},
        "required": [
            "PropertyName"
        ]
    }},
    "description": "Brief description of what this schema captures"
}}"""

        schema_generation_user = f"""
        {generated_user_prompt}

        Analyze the provided content and create a JSON schema that would capture the key information the user is interested in.

        REQUIREMENTS:
        1. Use only valid JSON schema types: "object", "array", "string", "number", "boolean"
        2. For arrays, always include "items" with the type of array elements
        3. Every property must have a "description" field
        4. Do NOT use unsupported fields like "format", "pattern", "enum", "minLength", "maxLength", etc.
        5. Keep property names descriptive and user-friendly
        6. The schema should be practical and useful for structuring the output data

        Example of correct format:
        {{
            "PropertyName": {{
                "type": "string",
                "description": "What this property represents"
            }},
            "ListProperty": {{
                "type": "array",
                "items": {{
                    "type": "string"
                }},
                "description": "Description of the list contents"
            }}
        }}
        """

        # Handle file content if provided
        gemini_file = None
        temp_file_path = None

        if file_content:
            logger.info(f"Processing file content for Gemini upload - Size: {len(file_content)} bytes")
            # Create temporary file for Gemini upload
            with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file_name or 'upload'}") as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            logger.info(f"Created temporary file: {temp_file_path}")

            # Upload to Gemini
            logger.info("Uploading file to Gemini API")
            gemini_file = await async_upload_to_gemini(temp_file_path, mime_type)
            logger.info(f"File uploaded to Gemini successfully: {gemini_file.name if gemini_file else 'None'}")
        
        # Generate schema
        logger.info("Creating schema generation model")
        schema_model = get_model(api_key, "gemini-2.0-flash", generation_config, schema_generation_system)

        if gemini_file:
            logger.info("Generating schema using uploaded file content")
            schema_response = await async_generate_content(schema_model, gemini_file, schema_generation_user)
        else:
            # For URL or text-only analysis, use direct model generation
            content_context = f"Content URL: {url}" if url else "No specific content provided"
            full_prompt = f"{schema_generation_user}\n\nContext: {content_context}"

            logger.info(f"Generating schema using URL/text context: {content_context}")
            def _generate_schema():
                response = schema_model.generate_content(full_prompt)
                return response
            schema_response = await loop.run_in_executor(None, _generate_schema)

        logger.info(f"Schema generation completed - Response length: {len(schema_response.text)} chars")

        # Clean up temporary file
        if temp_file_path and os.path.exists(temp_file_path):
            logger.debug(f"Cleaning up temporary file: {temp_file_path}")
            os.unlink(temp_file_path)
        
        # Parse schema response
        logger.info("Parsing schema generation response")
        try:
            schema_data = json.loads(schema_response.text)
            response_schema = schema_data.get("schema", {})
            description = schema_data.get("description", "Generated schema for content analysis")

            # Validate schema format
            if not _validate_schema_format(response_schema):
                logger.warning("Generated schema doesn't follow required format, applying corrections")
                response_schema = _fix_schema_format(response_schema)

            logger.info("Successfully parsed schema response")
            logger.debug(f"Generated schema properties count: {len(response_schema.get('properties', {}))}")
        except json.JSONDecodeError:
            logger.error(f"Failed to parse schema response: {schema_response.text}")
            # Fallback schema following the required format
            response_schema = {
                "type": "object",
                "properties": {
                    "content_analysis": {
                        "type": "string",
                        "description": "General analysis of the provided content"
                    },
                    "key_points": {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "description": "Important points extracted from the content"
                    },
                    "confidence_score": {
                        "type": "number",
                        "description": "Confidence level of the analysis (0.0 to 1.0)"
                    }
                },
                "required": ["content_analysis", "key_points", "confidence_score"]
            }
            description = "Fallback schema for general content analysis due to parsing error"
            logger.warning("Using fallback schema due to parsing error")

        # Calculate costs
        logger.info("Calculating API usage costs")
        total_cost = price_nep(prompt_response) + price_nep(schema_response)
        prompt_tokens = prompt_response.usage_metadata.prompt_token_count + schema_response.usage_metadata.prompt_token_count
        completion_tokens = prompt_response.usage_metadata.candidates_token_count + schema_response.usage_metadata.candidates_token_count
        logger.info(f"Cost calculation completed - Total: {total_cost} NEP, "
                   f"Prompt tokens: {prompt_tokens}, Completion tokens: {completion_tokens}")

        # Generate presigned URL for uploaded files
        logger.info("Generating content URL for response")
        content_url = url
        if file_content and user_tenant_info:
            # Generate presigned URL for the uploaded file
            from datetime import timedelta
            object_name = f"schema_generation/{user_tenant_info.user.id}/{file_name}"
            try:
                logger.debug(f"Generating presigned URL for object: {object_name}")
                content_url = user_tenant_info.minio_client.presigned_get_object(
                    bucket_name=user_tenant_info.minio_bucket_name,
                    object_name=object_name,
                    expires=timedelta(hours=24)  # URL expires in 24 hours
                )
                logger.info("Presigned URL generated successfully")
            except Exception as e:
                logger.warning(f"Failed to generate presigned URL: {str(e)}")
                content_url = file_name or "uploaded_file"

        # Build the final response in the requested format
        logger.info("Building final response structure")
        result = {
            "input": {
                "content": content_url,
                "content_type": "url" if url else "file"
            },
            "generation_schema": {
                "output_schema": {
                    "temperature": generation_config.get("temperature", 1),
                    "top_p": generation_config.get("top_p", 0.95),
                    "top_k": generation_config.get("top_k", 40),
                    "max_output_tokens": generation_config.get("max_output_tokens", 2048),
                    "response_schema": response_schema,
                    "response_mime_type": generation_config.get("response_mime_type", "application/json")
                },
                "prompts": {
                    "system": system_prompt,
                    "user": generated_user_prompt
                },
                "description": description
            },
            "cost_info": {
                "total_cost_nep": total_cost,
                "prompt_tokens": prompt_tokens,
                "completion_tokens": completion_tokens
            }
        }

        logger.info("Schema generation completed successfully")
        return result

    except Exception as e:
        logger.error(f"Schema generation failed with error: {str(e)}", exc_info=True)
        raise Exception(f"Schema generation failed: {str(e)}")
