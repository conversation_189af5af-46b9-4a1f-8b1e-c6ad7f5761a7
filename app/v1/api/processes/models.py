"""
Pydantic models for the processes API
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, Union, Literal
from enum import Enum

class ContentType(str, Enum):
    FILE = "file"
    URL = "url"

class InputContent(BaseModel):
    """Input content specification"""
    content: str = Field(..., description="File upload or URL")
    content_type: ContentType = Field(..., description="Type of content: file or url")

class GenerationConfig(BaseModel):
    """Configuration for Gemini generation"""
    temperature: float = Field(1.0, ge=0.0, le=2.0, description="Controls randomness in generation")
    top_p: float = Field(0.95, ge=0.0, le=1.0, description="Nucleus sampling parameter")
    top_k: int = Field(40, ge=1, le=100, description="Top-k sampling parameter")
    max_output_tokens: int = Field(2048, ge=1, le=8192, description="Maximum number of output tokens")

class SchemaGenerationRequest(BaseModel):
    """Request model for schema generation"""
    input: Optional[InputContent] = Field(None, description="Input content (optional if using file upload)")
    user_prompt: str = Field(..., description="Description of what schema to generate")
    generation_config: Optional[GenerationConfig] = Field(default_factory=GenerationConfig, description="Generation configuration")

class ResponseSchema(BaseModel):
    """Generated response schema"""
    type: str = Field("object", description="Schema type")
    properties: Dict[str, Any] = Field(..., description="Schema properties")
    required: Optional[list] = Field(None, description="Required fields")

class OutputSchema(BaseModel):
    """Output schema configuration"""
    temperature: float
    top_p: float
    top_k: int
    max_output_tokens: int
    response_schema: ResponseSchema
    response_mime_type: str = Field("application/json", description="Response MIME type")

class Prompts(BaseModel):
    """System and user prompts"""
    system: str = Field(..., description="System prompt")
    user: str = Field(..., description="User prompt")

class GenerationSchema(BaseModel):
    """Generated schema details"""
    output_schema: OutputSchema
    prompts: Prompts
    description: str = Field(..., description="Description of the schema purpose")

class SchemaGenerationResponse(BaseModel):
    """Response model for schema generation"""
    input: InputContent
    generation_schema: GenerationSchema
    cost_info: Optional[Dict[str, Any]] = Field(None, description="Cost information for the API call")

class SchemaGenerationError(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error message")
    error_type: str = Field(..., description="Type of error")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
