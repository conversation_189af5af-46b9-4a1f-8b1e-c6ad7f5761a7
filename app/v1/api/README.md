## Overview
The huigh Level Process API allows you to run various data processing tasks on your content. It supports both synchronous and asynchronous processing modes, giving you flexibility in how you handle long-running tasks.

The low level jobs and projects APIs are used to manage the processing jobs and can be complicated for simpler tasks. 

(High Level API Recommended)

> Note: Each endpoint mentioned below has detailed documentation available in the Swagger UI. Visit `/docs` in your browser and search for the specific endpoint to see its complete specification, parameters, and response schemas.

## Getting Started

### 1. List Available Processes
First, check what processes are available for your use:

- Make a `GET` request to:  
  `/v1/processes` *(See detailed specification in Swagger UI `/docs`)*

This will return a list of all available processes with their descriptions and required parameters.

### 2. Submit Content for Processing

- Make a `POST` request to:  
  `/v1/processes/{process_name}/run` *(See detailed specification in Swagger UI `/docs`)*

- Include:
  - Required input files or URLs (as specified by the process)
  - `wait_for_completion=true` for synchronous processing  
    OR  
    `wait_for_completion=false` for asynchronous processing

(Note: When sending audio urls through the swagger tool, unselect the Send Empty Value for the `files` field if you are not sending any files.)
### 3. Handle the Response

- If `wait_for_completion = true`
    - The API will return the result in the same response
    - Processing is complete once you receive the response

- If `wait_for_completion = false`
    - You'll receive a job ID for tracking the process
    
    #### Check Job Status
    - Make a `GET` request to:  
      `/v1/jobs/status/{job_id}` *(See detailed specification in Swagger UI `/docs`)*
    - Keep checking until the status is `"completed"`

    #### Get Job Output
    - Once the job is completed, make a `GET` request to:  
      `/v1/jobs/output/{job_id}` *(See detailed specification in Swagger UI `/docs`)*
    - You'll receive the final processing output

#### Get Presigned URLs
- If you need to access job input/output files and the URLs have expired:
  - Make a `GET` request to:  
    `/v1/jobs/media/presigned-url` *(See detailed specification in Swagger UI `/docs`)*
  - This will return a new presigned URL for the specified object
  - URLs are valid for a limited time period

