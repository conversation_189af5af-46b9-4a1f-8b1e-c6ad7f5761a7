from pydantic import BaseModel, HttpUrl, Field
from typing import Optional, Dict, Any, List
from enum import Enum
from datetime import datetime, timezone

class WebhookStatus(str, Enum):
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"

    def __str__(self):
        return self.value

class WebhookCreate(BaseModel):
    url: HttpUrl
    job_id: str
    payload: Dict[str, Any]
    project_id: Optional[str] = None  # Link to project

class WebhookRequest(WebhookCreate):
    """Represents an incoming trigger with optional timestamp."""
    triggered_at: Optional[datetime] = Field(default_factory=lambda: datetime.now(timezone.utc))

class WebhookResponse(BaseModel):
    webhook_id: str
    status: WebhookStatus
    job_id: str
    response_status_code: Optional[int] = Field(None, ge=100, le=599)  # HTTP status code validation
    response_body: Optional[Dict[str, Any]] = None
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    presigned_url: Optional[HttpUrl] = Field(None, exclude=True)  # Kept for internal use but excluded from response
    presigned_urls: List[HttpUrl] = Field(default_factory=list)  # List of presigned URLs
    completed_at: Optional[datetime] = None

    class Config:
        json_schema_extra = {
            "example": {
                "webhook_id": "123456789",
                "status": "success",
                "job_id": "job123",
                "presigned_urls": ["https://example.com/url1", "https://example.com/url2"]
            }
        }