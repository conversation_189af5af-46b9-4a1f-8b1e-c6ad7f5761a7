from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from datetime import datetime, timezone
from typing import Dict, Any
import httpx
from bson import ObjectId

from app.models.user import UserTenantDB
from app.core.security import require_roles
from app.core.helper.logger import setup_new_logging
from .models import WebhookRequest, WebhookResponse, WebhookStatus, WebhookCreate

logger = setup_new_logging(__name__)
router = APIRouter(tags=["Webhooks"], prefix="/webhook")


@router.post("/trigger", response_model=WebhookResponse)
async def api_trigger_webhook(
    webhook_data: WebhookCreate,
    background_tasks: BackgroundTasks,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    return await trigger_webhook(webhook_data, background_tasks, user_tenant_info)


@router.get("/check/{webhook_id}", response_model=WebhookResponse)
async def check_webhook_status(webhook_id: str, user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))):
    doc = await user_tenant_info.async_db.webhooks.find_one({"_id": ObjectId(webhook_id)})
    print(doc)
    if not doc:
        raise HTTPException(status_code=404, detail="Webhook not found")

    presigned_url = doc.get("payload", {}).get("presigned_url") or doc.get("presigned_url")
    presigned_urls = doc.get("presigned_urls", [])

    # If we have presigned_urls in the document, use those
    # Otherwise, try to extract them from the payload outputs
    if not presigned_urls:
        outputs = doc.get("payload", {}).get("outputs", [])
        presigned_urls = [output.get("presigned_url") for output in outputs if output.get("presigned_url")]

    # If we still don't have any URLs in the list but have a single URL, add it to the list
    if not presigned_urls and presigned_url:
        presigned_urls = [presigned_url]

    return WebhookResponse(
        webhook_id=webhook_id,
        status=doc["status"],
        job_id=doc.get("job_id", ""),
        response_status_code=doc.get("response_status_code"),
        response_body=None,
        presigned_url=presigned_url,  # Keep for backward compatibility
        presigned_urls=presigned_urls,
        created_at=datetime.fromisoformat(doc["created_at"]),
        completed_at=datetime.fromisoformat(doc["completed_at"]) if doc.get("completed_at") else None
    )


async def trigger_webhook(
    webhook_data: WebhookCreate,
    background_tasks: BackgroundTasks,
    user_tenant_info: UserTenantDB
) -> WebhookResponse:
    db = user_tenant_info.async_db.webhooks
    created_at = datetime.now(timezone.utc)

    payload_summary = {
        "job_id": webhook_data.payload.get("job_id", ""),
        "status": webhook_data.payload.get("status", ""),
        "project_id": webhook_data.payload.get("project_id", ""),
        "presigned_url": webhook_data.payload.get("presigned_url", "")
    }

    webhook_record = {
        "url": str(webhook_data.url),
        "job_id": webhook_data.job_id,
        "payload": payload_summary,
        "status": str(WebhookStatus.PENDING),
        "created_at": created_at.isoformat(),
        "completed_at": None,
        "response_status_code": None,
    }

    result = await db.insert_one(webhook_record)
    webhook_id = str(result.inserted_id)

    background_tasks.add_task(
        send_webhook,
        webhook_id,
        str(webhook_data.url),
        webhook_data.payload,
        user_tenant_info
    )

    # Get the first URL for backward compatibility
    presigned_url = webhook_data.payload.get("outputs", [{}])[0].get("presigned_url") if webhook_data.payload.get("outputs") else None

    # Extract all presigned URLs from outputs
    presigned_urls = []
    for output in webhook_data.payload.get("outputs", []):
        if output.get("presigned_url"):
            presigned_urls.append(output.get("presigned_url"))

    # Store the presigned_urls in the database
    if presigned_urls:
        await db.update_one({"_id": ObjectId(webhook_id)}, {"$set": {"presigned_urls": presigned_urls}})

    return WebhookResponse(
        webhook_id=webhook_id,
        status=WebhookStatus.PENDING,
        job_id=webhook_data.job_id,
        response_status_code=None,
        response_body=None,
        presigned_url=presigned_url,  # Keep for backward compatibility
        presigned_urls=presigned_urls,
        created_at=created_at,
        completed_at=None
    )


async def send_webhook(webhook_id: str, url: str, payload: Dict[str, Any], user_tenant_info: UserTenantDB):
    db = user_tenant_info.async_db.webhooks
    now = datetime.now(timezone.utc)

    # Get the first URL for backward compatibility
    presigned_url = payload.get("outputs", [{}])[0].get("presigned_url") if payload.get("outputs") else None

    # Extract all presigned URLs from outputs
    presigned_urls = []
    for output in payload.get("outputs", []):
        if output.get("presigned_url"):
            presigned_urls.append(output.get("presigned_url"))

    try:
        logger.info(f"Sending webhook to {url}")
        logger.info(f"Webhook payload summary: {{'job_id': {payload.get('job_id')}, 'status': {payload.get('status')}, 'project_id': {payload.get('project_id')}, 'outputs_count': {len(payload.get('outputs', []))}}}")

        # Update the database with all presigned URLs
        update_data = {}
        if presigned_url:
            update_data["payload.presigned_url"] = presigned_url
        if presigned_urls:
            update_data["presigned_urls"] = presigned_urls

        if update_data:
            await db.update_one({"_id": ObjectId(webhook_id)}, {"$set": update_data})

        # Include only the presigned_urls list in the webhook response
        webhook_response_data = {
            "webhook_id": webhook_id,
            "status": str(WebhookStatus.SUCCESS),
            "job_id": payload.get("job_id", ""),
            "presigned_urls": presigned_urls
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(
                url,
                json=webhook_response_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )

        response_status = response.status_code
        response_body = None

        try:
            response_body = response.json()
            # Update presigned_url for backward compatibility
            presigned_url = response_body.get("presigned_url", presigned_url)

            # Update presigned_urls if provided in the response
            if response_body.get("presigned_urls"):
                presigned_urls = response_body.get("presigned_urls")
        except Exception as e:
            logger.warning(f"Could not parse response JSON: {e}")

        update_data = {
            "status": WebhookStatus.SUCCESS,
            "completed_at": now.isoformat(),
            "response_status_code": response_status,
            "presigned_url": presigned_url,
            "payload.presigned_url": presigned_url,
            "presigned_urls": presigned_urls
        }
        await db.update_one({"_id": ObjectId(webhook_id)}, {"$set": update_data})

        return WebhookResponse(
            webhook_id=webhook_id,
            status=WebhookStatus.SUCCESS,
            job_id=payload.get("job_id", ""),
            response_status_code=response_status,
            response_body=None,
            presigned_url=presigned_url,
            presigned_urls=presigned_urls,
            created_at=now,
            completed_at=now
        )

    except Exception as e:
        logger.error(f"Error sending webhook: {e}")

        update_data = {
            "status": WebhookStatus.FAILED,
            "completed_at": now.isoformat(),
            "error_message": str(e),
            "presigned_url": presigned_url,
            "payload.presigned_url": presigned_url,
            "presigned_urls": presigned_urls
        }
        await db.update_one({"_id": ObjectId(webhook_id)}, {"$set": update_data})

        try:
            async with httpx.AsyncClient() as client:
                await client.post(
                    url,
                    json={
                        "webhook_id": webhook_id,
                        "status": str(WebhookStatus.FAILED),
                        "job_id": payload.get("job_id", ""),
                        "presigned_urls": presigned_urls,
                        "error": str(e)
                    },
                    headers={"Content-Type": "application/json"},
                    timeout=30
                )
        except Exception as notify_error:
            logger.error(f"Failed to send failure notification: {notify_error}")

        return WebhookResponse(
            webhook_id=webhook_id,
            status=WebhookStatus.FAILED,
            job_id=payload.get("job_id", ""),
            response_status_code=None,
            response_body=None,
            presigned_url=presigned_url,
            presigned_urls=presigned_urls,
            created_at=now,
            completed_at=now
        )
