from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from datetime import datetime


class VoiceCloningEntry(BaseModel):
    """Schema for a single voice cloning entry."""
    text: str = Field(..., description="Text to convert to speech")
    preset_voice: str = Field(..., description="Voice preset (e.g., 'formal', 'casual')")
    user_voice_id: str = Field(..., description="Voice ID to clone")
    quality: Optional[str] = Field("20", description="Quality parameter (default: '20')")


class VoiceCloningRequest(BaseModel):
    """Schema for voice cloning request."""
    entries: List[VoiceCloningEntry] = Field(..., description="List of voice cloning entries")
    job_id: str = Field(..., description="Job ID for tracking")


class VoiceCloningResult(BaseModel):
    """Schema for voice cloning result."""
    status: str = Field(..., description="Status of the operation ('success' or 'error')")
    entry: Optional[Dict[str, Any]] = Field(None, description="Original entry that was processed")
    result: Optional[Dict[str, Any]] = Field(None, description="API response result")
    error: Optional[str] = Field(None, description="Error message if status is 'error'")
    job_id: str = Field(..., description="Job ID for tracking")
    timestamp: str = Field(..., description="ISO timestamp of when the operation was performed")


class VoiceCloningResponse(BaseModel):
    """Schema for voice cloning response."""
    results: List[VoiceCloningResult] = Field(..., description="List of results from voice cloning operations")
    total_entries: int = Field(..., description="Total number of entries processed")
    successful_entries: int = Field(..., description="Number of successful entries")
    failed_entries: int = Field(..., description="Number of failed entries")
    job_id: str = Field(..., description="Job ID for tracking")
    processing_time: Optional[float] = Field(None, description="Total processing time in seconds")


# Example usage:
"""
# JSON file format (voice_cloning_entries.json):
[
    {
        "text": "Hello, how are you today?",
        "preset_voice": "formal",
        "user_voice_id": "6846a63e8c246ef2182ea246",
        "quality": "20"
    },
    {
        "text": "This is a test message for voice cloning.",
        "preset_voice": "casual",
        "user_voice_id": "6846a63e8c246ef2182ea247",
        "quality": "30"
    }
]

# JSONL file format (voice_cloning_entries.jsonl):
{"text": "Hello, how are you today?", "preset_voice": "formal", "user_voice_id": "6846a63e8c246ef2182ea246", "quality": "20"}
{"text": "This is a test message for voice cloning.", "preset_voice": "casual", "user_voice_id": "6846a63e8c246ef2182ea247", "quality": "30"}
""" 