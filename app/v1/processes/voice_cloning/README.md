# Voice Cloning Process

This module provides voice cloning functionality using the TTS API. It processes voice cloning entries from JSON or JSONL files and makes parallel API calls to clone voices.

## Features

- **Asynchronous Processing**: All API calls are made asynchronously for better performance
- **File Format Support**: Supports both JSON and JSONL file formats
- **Error Handling**: Comprehensive error handling with detailed logging
- **Validation**: Validates required fields and provides meaningful error messages
- **Parallel Processing**: Processes multiple entries in parallel using `asyncio.gather()`

## API Endpoint

The voice cloning process uses the following API endpoint:
- **URL**: `https://tts-api.nextai.asia/v2/speak_v2`
- **Method**: POST
- **Content-Type**: `application/x-www-form-urlencoded`

## Required Fields

Each entry in the input file must contain the following fields:

- `text` (required): The text to convert to speech
- `preset_voice` (required): Voice preset (e.g., "formal", "casual")
- `user_voice_id` (required): Voice ID to clone
- `quality` (optional): Quality parameter (default: "20")

## File Formats

### JSON Format

```json
[
    {
        "text": "Hello, how are you today?",
        "preset_voice": "formal",
        "user_voice_id": "6846a63e8c246ef2182ea246",
        "quality": "20"
    },
    {
        "text": "This is a test message for voice cloning.",
        "preset_voice": "casual",
        "user_voice_id": "6846a63e8c246ef2182ea247",
        "quality": "30"
    }
]
```

### JSONL Format

```jsonl
{"text": "Hello, how are you today?", "preset_voice": "formal", "user_voice_id": "6846a63e8c246ef2182ea246", "quality": "20"}
{"text": "This is a test message for voice cloning.", "preset_voice": "casual", "user_voice_id": "6846a63e8c246ef2182ea247", "quality": "30"}
```

## Usage

### Basic Usage

```python
from app.v1.processes.voice_cloning import VoiceCloning
from app.models.user import UserTenantDB

# Initialize with user tenant info
user_tenant_info = UserTenantDB(...)
voice_cloner = VoiceCloning(user_tenant_info)

# Process voice cloning entries
results = await voice_cloner.voice_clone_pipeline("path/to/entries.json", "job_123")
```

### Example Response

```python
[
    {
        "status": "success",
        "entry": {
            "text": "Hello, how are you today?",
            "preset_voice": "formal",
            "user_voice_id": "6846a63e8c246ef2182ea246",
            "quality": "20"
        },
        "result": {
            "message": "speak_v2 workflow completed successfully",
            "final_audio_url": "https://minio.nextai.asia/voice/speak_v2/...",
            "text": "hello how are you today",
            "preset_voice": "formal",
            "user_voice_id": "6846a63e8c246ef2182ea246",
            "user_id": "1234323243",
            "user_name": "Bibek",
            "final_file_size": 226618,
            "created_at": "2025-08-06T05:33:48.082221",
            "minio_path": "speak_v2/1234323243/Bibek/speak_v2_output_...",
            "workflow_steps": {
                "voice_generation_tts_size": 114332,
                "voice_cloning_size": 0,
                "timbre_transfer_size": 226618
            }
        },
        "job_id": "job_123",
        "timestamp": "2025-01-27T10:30:00.000000"
    }
]
```

## Error Handling

The process handles various types of errors:

- **File Not Found**: If the input file doesn't exist
- **Invalid JSON**: If the file contains malformed JSON
- **Missing Required Fields**: If required fields are missing or empty
- **HTTP Errors**: Network and API errors are caught and logged
- **Request Timeouts**: 5-minute timeout for each API call

### Error Response Example

```python
{
    "status": "error",
    "entry": {
        "text": "Hello, how are you today?",
        "preset_voice": "formal",
        "user_voice_id": "6846a63e8c246ef2182ea246",
        "quality": "20"
    },
    "error": "HTTP 400: Invalid voice ID",
    "job_id": "job_123",
    "timestamp": "2025-01-27T10:30:00.000000"
}
```

## Logging

The process provides detailed logging:

- **Info**: Processing start/completion, successful operations
- **Warning**: Invalid JSON lines in JSONL files
- **Error**: HTTP errors, request errors, validation errors

## Performance

- **Parallel Processing**: All entries are processed concurrently
- **Timeout**: 5-minute timeout per API call
- **Memory Efficient**: Processes files line by line for JSONL format

## Testing

Run the example script to test the voice cloning process:

```bash
python app/v1/processes/voice_cloning/example.py
```

## Dependencies

- `httpx`: For async HTTP requests
- `pydantic`: For data validation (in models.py)
- `asyncio`: For async/await functionality
- `json`: For JSON parsing
- `pathlib`: For file path handling

## Notes

- The API uses Bearer token authentication
- Voice IDs must be valid ObjectId format
- Quality parameter is optional and defaults to "20"
- All timestamps are in UTC ISO format 