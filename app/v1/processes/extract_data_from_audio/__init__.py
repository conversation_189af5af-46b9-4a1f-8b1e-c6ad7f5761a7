import os
from google.ai.generativelanguage_v1beta.types import content
from typing import Dict
from app.v1.processes.gemini_helpers import get_model, async_upload_to_gemini, async_generate_content, price_nep
import json
from app.core.helper.logger import setup_new_logging

logger = setup_new_logging(__name__)

analyse_speech_prompt = """
Following is the conversation between a Sales Agent and a potential customer.
Analyse the conversation and extract the following information:
"""

generation_config = {
    "temperature": 1,
    "top_p": 0.95,
    "top_k": 40,
    "max_output_tokens": 8192,
    "response_schema": {
        "type": "object",
        "properties": {
            "Speech Tone": {
                "type": "string",
                "description": "Overall tone of the agent in the conversation (e.g., Positive, Negative, Aggressive, Dismissive).",
            },
            "Politeness": {
                "type": "integer",
                # "minimum": 1,
                # "maximum": 5,
                "description": "Rate politeness of the conversation on a scale of 1 to 5.",
            },
            "Politeness Description": {
                "type": "string",
                "description": "Describe the politeness of the conversation.",
            },
            "Resolution Effort": {
                "type": "integer",
                # "minimum": 1,
                # "maximum": 5,
                "description": "Rate the resolution effort of the conversation on a scale of 1 to 5.",
            },
            "Product Mentions (Phrases)": {
                "type": "array",
                "items": {
                    "type": "string"
                },
                "description": "An array of phrases from the conversation that likely refer to products or services, even if they are not exact product names. \nReference Product Names: [TSC 1st paper, japanese N5... ]"
            },
            "Potential Customer Intention": {
                "type": "string",
                "description": "Describe the potential customer's intention in the conversation. whether they are interested in the product or not.",
            },
            "Resolution Effort Description": {
                "type": "string",
                "description": "Describe the resolution effort of the conversation.",
            },
            "Clarity": {
                "type": "integer",
                # "minimum": 1,
                # "maximum": 5,
                "description": "Rate clarity of the conversation on a scale of 1 to 5.",
            },
            "Clarity Description": {
                "type": "string",
                "description": "Describe the clarity of the conversation.",
            },
            "Talk Ratio (Agent:Customer)": {
                "type": "string",
                "description": "Evaluate the proportion of speaking time between the agent and the customer, expressed as a ratio (e.g., 60:40).",
            },
            "Overall Agent Rating": {
                "type": "integer",
                # "minimum": 1,
                # "maximum": 5,
                "description": "Rate overall performance of the agent on a scale of 1 to 5.",
            },
            "Overall Agent Performance": {
                "type": "string",
                "description": "Describe the overall performance of the agent.",
            },
        },
        "required": [
            "Speech Tone",
            "Politeness",
            "Politeness Description",
            "Resolution Effort",
            "Resolution Effort Description",
            "Clarity",
            "Clarity Description",
            "Talk Ratio (Agent:Customer)",
            "Overall Agent Rating",
            "Overall Agent Performance",
            "Product Mentions (Phrases)",
            "Potential Customer Intention"
        ],
    },
    "response_mime_type": "application/json",
}

async def extract_audio_info(audio_path: str, api_key: str, system_instruction: str = None, model_name: str = "gemini-2.0-flash-lite") -> Dict:
    logger.info(f"Starting audio analysis using model: {model_name}")

    model = get_model(api_key, model_name, generation_config, system_instruction)
    file = await async_upload_to_gemini(audio_path, mime_type="audio/mpeg")
    response = await async_generate_content(model, file, analyse_speech_prompt)

    try:
        analysis_data = json.loads(response.text)
    except json.JSONDecodeError:
        logger.error(f"Failed to parse JSON response from {model_name} for audio analysis")
        raise

    price = price_nep(response, model_name)
    logger.info(f"Audio analysis completed using {model_name}. "
               f"Tokens used - Input: {response.usage_metadata.prompt_token_count}, "
               f"Output: {response.usage_metadata.candidates_token_count}, "
               f"Cost: {price:.4f} NEP")

    return {
        "analysis": analysis_data,
        "price_nep": price,
        "usage_metadata": {
            "prompt_token_count": response.usage_metadata.prompt_token_count,
            "candidates_token_count": response.usage_metadata.candidates_token_count,
        }
    }

# Example usage:
# import asyncio
# result = asyncio.run(extract_audio_info("/path/to/audio.mp3", api_key="YOUR_API_KEY"))
# print(result)

if __name__ == "__main__":
    import asyncio
    from dotenv import load_dotenv
    load_dotenv()
    result = asyncio.run(extract_audio_info("/Users/<USER>/Documents/test_gits/ai_testing/audio_files/recording (3).mp3", api_key=os.environ.get("GOOGLE_API_KEY")))
    print(result)