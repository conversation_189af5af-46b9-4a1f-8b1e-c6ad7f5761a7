import os
import json
import asyncio
import mimetypes
from io import BytesIO
from datetime import datetime, timezone
from typing import List, Dict, Any,Tuple
from urllib.parse import unquote, urlparse
from bson import ObjectId
from .models import ProcessConfig
import google.generativeai as genai
from app.models.user import UserTenantDB
from app.v1.api.jobs.models import JobStatus
from app.v1.processes.gemini_helpers import async_generate_content, async_upload_to_gemini, price_nep, get_model
from app.core.helper.logger import setup_new_logging
import requests
logger = setup_new_logging(__name__)


class GenericEntity:
    """
    A flexible entity extraction class to handle inputs (text, URL, etc.) and extract
    structured data using Gemini with a user-defined schema.
    """

    def __init__(self, user_tenant_info: UserTenantDB, model_name: str = "gemini-2.0-flash-lite"):
        self.user_tenant_info = user_tenant_info
        self.minio_client = user_tenant_info.minio_client
        self.minio_bucket = user_tenant_info.minio_bucket_name
        self.model_name = model_name
        api_keys = user_tenant_info.db.config.find_one({"name": "api-keys"}).get("value")
        self.api_key = api_keys.get("GOOGLE_API_KEY")
        genai.configure(api_key=self.api_key)
        logger.info(f"GenericEntity initialized with model: {self.model_name}")

    def set_model(self, model_name: str):
        """
        Set the model name to use for entity extraction.

        Args:
            model_name: Name of the model ("gemini-2.0-flash" or "gemini-2.0-flash-lite")
        """
        if model_name not in ["gemini-2.0-flash", "gemini-2.0-flash-lite"]:
            raise ValueError(f"Unsupported model: {model_name}. Use 'gemini-2.0-flash' or 'gemini-2.0-flash-lite'")

        old_model = self.model_name
        self.model_name = model_name
        logger.info(f"Model changed from {old_model} to {self.model_name}")

    async def get_api_key(self) -> str:
        """
        Get the Google API key from the config collection.

        Returns:
            API key string
        """
        config = await self.user_tenant_info.async_db.config.find_one({"name": "api-keys"})
        if not config or "GOOGLE_API_KEY" not in config.get("value", {}):
            raise ValueError("Google API key not found in configuration")
        return config["value"]["GOOGLE_API_KEY"]


    def get_model(self, generation_schema: Dict[str, Any] = None,system_instruction: str = None):
        """
        Get a Gemini model with the specified generation schema.

        Args:
            generation_schema: Custom generation schema for the model

        Returns:
            Configured Gemini model
        """
        if generation_schema is None:
            generation_schema = {
                "temperature": 1,
                "top_p": 0.95,
                "top_k": 40,
                "max_output_tokens": 8192,
                "response_mime_type": "application/json",
            }

        return genai.GenerativeModel(
            model_name=self.model_name,
            generation_config=generation_schema,
            system_instruction=system_instruction,
        )

    def calculate_job_usage_metadata_and_pricing(self, total_usage_metadata_n_pricing: dict) -> Dict[str, Any]:
        """Calculate usage metadata and pricing for the job."""
        print("Calculating job usage metadata and pricing...")
        print(total_usage_metadata_n_pricing)

        # Initialize totals
        total_price_nep = 0
        total_prompt_tokens = 0
        total_completion_tokens = 0

        # Process usage metadata
        for usage_metadata in total_usage_metadata_n_pricing.get("usage_metadatas", []):
            # Handle entity extraction usage metadata
            if "entity_extraction" in usage_metadata:
                extraction_data = usage_metadata["entity_extraction"]
                total_prompt_tokens += extraction_data.get("prompt_token_count", 0)
                total_completion_tokens += extraction_data.get("candidates_token_count", 0)
            # Also handle direct usage metadata format
            elif "prompt_token_count" in usage_metadata:
                total_prompt_tokens += usage_metadata.get("prompt_token_count", 0)
                total_completion_tokens += usage_metadata.get("candidates_token_count", 0)

        # Process pricing data
        for pricing in total_usage_metadata_n_pricing.get("pricings", []):
            total_price_nep += pricing.get("total_price_nep", 0)

        return {
            "usage_metadata": {
                "total_prompt_tokens": total_prompt_tokens,
                "total_completion_tokens": total_completion_tokens
            },
            "pricing": {
                "total_price_nep": total_price_nep
            }
        }

    async def parse_json(self, file_path: str) -> ProcessConfig:
        """
        Parses a JSON file and returns a ProcessConfig object.

        Args:
            file_path: Path to the JSON file

        Returns:
            ProcessConfig object
        """
        try:
            with open(file_path, "r") as file:
                json_data = json.load(file)

            if not isinstance(json_data, dict):
                raise ValueError("JSON data is not a dictionary")

            processed_json = ProcessConfig(**json_data)
            return processed_json

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON: {str(e)}")
            raise ValueError(f"Invalid JSON format: {str(e)}")
    async def generate_entities(self, content: Any, generation_schema: Dict,
                               user_prompt: str, system_prompt: str = None) -> Dict:
        """
        Extract entities from content using Gemini.

        Args:
            content: Content to analyze (text, file, etc.)
            generation_schema: Schema for entity generation
            user_prompt: User query or message
            system_prompt: System-level instructions for the model

        Returns:
            Dictionary with extraction results

        Raises:
            ValueError: If JSON parsing fails or API key is missing
            Exception: If entity extraction fails for any other reason
        """
        api_key = await self.get_api_key()
        logger.info(f"Starting entity extraction using model: {self.model_name}")

        try:
            model = get_model(api_key, self.model_name, generation_schema, system_prompt)
            full_prompt = f"""
            Extract entities from the following content according to the schema.

            {json.dumps(generation_schema)}

            Additional Instructions:
            {user_prompt}

            Return the extracted entities in valid JSON format matching the provided schema.
            """

            response = await async_generate_content(
                model=model,
                prompt=full_prompt,
                file=content,
            )

            try:
                extracted_entities = json.loads(response.text)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response from {self.model_name}: {response.text}")
                raise ValueError(f"Failed to parse Gemini response as JSON: {str(e)}. Raw response: {response.text}")

            price = price_nep(response, self.model_name)
            logger.info(f"Entity extraction completed successfully using {self.model_name}. "
                       f"Tokens used - Input: {response.usage_metadata.prompt_token_count}, "
                       f"Output: {response.usage_metadata.candidates_token_count}, "
                       f"Cost: {price:.4f} NEP")

            return {
                "status": "success",
                "data": extracted_entities,
                "price_nep": price,
                "usage_metadata": {
                    "prompt_token_count": response.usage_metadata.prompt_token_count,
                    "candidates_token_count": response.usage_metadata.candidates_token_count,
                }
            }

        except ValueError:
            # Re-raise ValueError (JSON parsing errors, API key errors)
            raise
        except Exception as e:
            logger.error(f"Entity extraction failed using {self.model_name}: {str(e)}")
            raise Exception(f"Entity extraction failed using {self.model_name}: {str(e)}")

    async def process_media(self, media_id: str) -> Dict:
        """
        Process a media file and extract entities using Gemini.

        Args:
            media_id: ID of the media to process

        Returns:
            Dictionary with extraction results
        """
        media_collection = self.user_tenant_info.async_db.media
        media = await media_collection.find_one({"_id": ObjectId(media_id)})

        if not media or not media.get("object_name"):
            raise ValueError(f"Invalid media or missing object name for media {media_id}")

        object_name = media["object_name"]
        job_id = media.get("job_id")
        local_path = None

        try:
            # Update status to processing
            await media_collection.update_one(
                {"_id": ObjectId(media_id)},
                {"$set": {"status": JobStatus.INPROGRESS}}
            )

            # Get the file from MinIO
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                self.minio_client.get_object,
                self.minio_bucket,
                object_name
            )
            file_data = await loop.run_in_executor(None, response.read)

            # Save to temporary file
            temp_file_path = f"/tmp/{media_id}.json"
            with open(temp_file_path, "wb") as f:
                f.write(file_data)

            local_path = temp_file_path

            # Parse the JSON configuration file
            logger.info(f"Parsing JSON configuration file: {temp_file_path}")
            processed_json = await self.parse_json(temp_file_path)
            content = processed_json.input.content
            response_schema = processed_json.generation_schema.output_schema or {}

            # Get prompts from the correct location
            user_prompt = processed_json.generation_schema.prompts.user or "Extract entities from the provided content."
            system_prompt = processed_json.generation_schema.prompts.system or "You are a helpful assistant that extracts entities from content."
            print(user_prompt)
            print(system_prompt)
            # Handle content based on its typ   e
            if isinstance(content, str):
                if content.startswith(('http://', 'https://')):
                    # For URL content, just use the URL as text
                    logger.info(f"Processing URL as text content: {content}")
                else:
                    # Handle as direct text content
                    logger.info("Processing direct text content")
            else:
                error_msg = f"Content must be a string, got {type(content)}"
                logger.error(error_msg)
                raise ValueError(error_msg)
            print(content)
            # Extract entities
            logger.info("Generating entities with Gemini")
            downloaded_file_path = None
            try:
                if content.startswith(('http://', 'https://')):
                    # Download the file if it's a URL
                    downloaded_file_path, mimetype = await self.download_file(content)
                    # Upload to Gemini
                    uploaded_file = await async_upload_to_gemini(downloaded_file_path, mimetype)
                else:
                    # For direct text content, no need to download
                    uploaded_file = content

                result = await self.generate_entities(
                    uploaded_file,
                    system_prompt=system_prompt,
                    generation_schema=response_schema,
                    user_prompt=user_prompt
                )
            finally:
                # Clean up downloaded file if it exists and is different from the original local_path
                if downloaded_file_path and downloaded_file_path != local_path and os.path.exists(downloaded_file_path):
                    logger.info(f"Cleaning up downloaded file: {downloaded_file_path}")
                    os.remove(downloaded_file_path)

            # Save result to MinIO
            result_object_name = f"{job_id}/output/{media_id}_result.json"
            result_bytes = json.dumps(result).encode('utf-8')

            await loop.run_in_executor(
                None,
                self.minio_client.put_object,
                self.minio_bucket,
                result_object_name,
                BytesIO(result_bytes),
                len(result_bytes),
                'application/json'
            )

            # Update media status
            await media_collection.update_one(
                {"_id": ObjectId(media_id)},
                {
                    "$set": {
                        "status": JobStatus.COMPLETED,
                        "completed_at": datetime.now(timezone.utc),
                        "output_object_name": result_object_name,
                        "output_usage_metadata": result.get("usage_metadata", {}),
                        "output_pricing": result.get("price_nep", 0),
                    }
                }
            )

            return result

        except Exception as e:
            error_msg = f"Entity extraction failed: {str(e)}"
            logger.error(error_msg)

            # Update status on failure
            await media_collection.update_one(
                {"_id": ObjectId(media_id)},
                {
                    "$set": {
                        "status": JobStatus.FAILED,
                        "error": error_msg,
                        "completed_at": datetime.now(timezone.utc)
                    }
                }
            )

            # Re-raise the original exception instead of wrapping it
            raise

        finally:
            # Clean up: delete the downloaded file if it exists
            if local_path and os.path.exists(local_path):
                logger.info(f"Cleaning up temporary file: {local_path}")
                os.remove(local_path)

    async def download_file(self, url: str) -> Tuple[str, str]:
        """
        Download a file from a URL and return the local path and MIME type.

        Args:
            url: URL to download

        Returns:
            Tuple of (local_path, mime_type)
        """
        if not url.startswith(('http://', 'https://')):
            # If it's not a URL, return it as is
            return url, "application/json"

        try:
            logger.info(f"Downloading file from URL: {url}")
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()

            # Get the filename from the URL
            parsed_url = urlparse(url)
            filename = os.path.basename(parsed_url.path)
            if not filename:
                filename = f"downloaded_{datetime.now().strftime('%Y%m%d%H%M%S')}"

            # Determine MIME type
            content_type = response.headers.get('Content-Type', 'application/octet-stream')

            # Create a temporary file
            temp_file_path = f"/tmp/{filename}"
            with open(temp_file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            logger.info(f"Downloaded file to {temp_file_path}")
            return temp_file_path, content_type

        except requests.RequestException as e:
            logger.error(f"HTTP error downloading file from URL {url}: {str(e)}")
            raise ValueError(f"Failed to download file from URL due to HTTP error: {str(e)}")
        except IOError as e:
            logger.error(f"File I/O error downloading file from URL {url}: {str(e)}")
            raise ValueError(f"Failed to download file from URL due to file I/O error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error downloading file from URL {url}: {str(e)}")
            raise ValueError(f"Failed to download file from URL due to unexpected error: {str(e)}")

    async def process_multiple_media(self, media_ids: List[str]) -> List[Dict]:
        """
        Process multiple media files in parallel.

        Args:
            media_ids: List of media IDs to process

        Returns:
            List of dictionaries with results for each media
        """
        tasks = [self.process_media(media_id) for media_id in media_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "media_id": media_ids[i],
                    "error": str(result)
                })
            else:
                processed_results.append({
                    "media_id": media_ids[i],
                    "result": result
                })

        return processed_results

    async def extract_from_job(self, job_id: str) -> List[Dict]:
        """
        Extract entities from all media files in a job in parallel.

        Args:
            job_id: ID of the job to process

        Returns:
            List of dictionaries with results for each media
        """
        jobs_collection = self.user_tenant_info.async_db.jobs
        job = await jobs_collection.find_one({"_id": ObjectId(job_id)})

        if not job or not job.get("items"):
            raise ValueError(f"Invalid job or no items found for job {job_id}")

        # Update job status to processing
        await jobs_collection.update_one(
            {"_id": ObjectId(job_id)},
            {"$set": {"status": JobStatus.INPROGRESS, "updated_at": datetime.now(timezone.utc)}}
        )

        # Get media IDs from items
        media_ids = []
        process_items = {}  # Map to track process items by media_id

        # Initialize total usage metadata and pricing
        total_usage_metadata_n_pricing: dict = {
            "usage_metadatas": [], "pricings": []
        }

        for item in job["items"]:
            if item.get("input_id"):
                media_id = str(item["input_id"])
                media_ids.append(media_id)
                process_items[media_id] = item

        try:
            # Process all media files in parallel
            results = await self.process_multiple_media(media_ids)

            media_collection = self.user_tenant_info.async_db.media

            for result in results:
                media_id = result["media_id"]
                process_item = process_items[media_id]

                if "error" in result:
                    # Update process item status to failed
                    process_item["status"] = JobStatus.FAILED
                    process_item["error"] = result["error"]
                else:
                    # Create output media document
                    output_media = {
                        "filename": f"output_{media_id}.json",
                        "content_type": "application/json",
                        "bucket_name": self.minio_bucket,
                        "object_name": f"{job_id}/output/{media_id}_result.json",
                        "job_id": ObjectId(job_id),
                        "created_at": datetime.now(timezone.utc),
                        "created_by": job["created_by"],
                        "metadata": {
                            "source_media_id": ObjectId(media_id),
                            "process_type": "generic-entity-extraction",
                            "type": "output"
                        },
                        "output_usage_metadata": result["result"].get("usage_metadata", {}),
                        "output_pricing": {"total_price_nep": result["result"].get("price_nep", 0)}
                    }

                    # Collect usage metadata and pricing for job totals
                    usage_metadata = result["result"].get("usage_metadata", {})
                    if usage_metadata:
                        total_usage_metadata_n_pricing["usage_metadatas"].append({
                            "entity_extraction": usage_metadata
                        })

                    pricing = {"total_price_nep": result["result"].get("price_nep", 0)}
                    total_usage_metadata_n_pricing["pricings"].append(pricing)

                    # Insert output media
                    output_result = await media_collection.insert_one(output_media)

                    # Update process item
                    process_item["output_id"] = output_result.inserted_id
                    process_item["status"] = JobStatus.COMPLETED

            # Update job status and items
            all_successful = all(item.get("status") == JobStatus.COMPLETED for item in process_items.values())
            status = JobStatus.COMPLETED if all_successful else JobStatus.PARTIALLYCOMPLETED

            usage_metadata_and_pricing = self.calculate_job_usage_metadata_and_pricing(total_usage_metadata_n_pricing)

            await jobs_collection.update_one(
                {"_id": ObjectId(job_id)},
                {
                    "$set": {
                        "status": status,
                        "completed_at": datetime.now(timezone.utc),
                        "error_count": sum(1 for item in process_items.values() if item.get("status") == JobStatus.FAILED),
                        "items": list(process_items.values()),
                        "output_usage_metadata": usage_metadata_and_pricing["usage_metadata"],
                        "output_pricing": usage_metadata_and_pricing["pricing"],
                    }
                }
            )

            return results

        except Exception as e:
            # Update job status to failed
            await jobs_collection.update_one(
                {"_id": ObjectId(job_id)},
                {
                    "$set": {
                        "status": JobStatus.FAILED,
                        "error": str(e),
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            # Re-raise the original exception instead of wrapping it
            raise
