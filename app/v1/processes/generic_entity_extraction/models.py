from pydantic import BaseModel, Field, AnyUrl
from typing import Dict, Optional, Union, Any


class Input(BaseModel):
    content: Optional[Union[str, Dict[str, Any], AnyUrl]] = Field(
        None, description="Content to analyze; can be string, link, or dict"
    )
    content_type: Optional[str] = Field(None, description="Optional hint about content type")


class Prompts(BaseModel):
    system: Optional[str] = Field(None, description="System-level instruction")
    user: Optional[str] = Field(None, description="User query or message")


class GenerationSchema(BaseModel):
    output_schema: Optional[Dict[str, Any]] = Field(
        None, description="JSON Schema definition to guide generation"
    )
    description: Optional[str] = Field(None, description="Description of the generation schema")
    response_mime_type: Optional[str] = Field(None, description="Expected response MIME type")
    prompts: Optional[Prompts] = Field(None, description="Prompt instructions at the schema level")


class ProcessConfig(BaseModel):
    input: Input
    generation_schema: GenerationSchema
    description: Optional[str] = Field(None, description="What the generation is for")
    prompts: Optional[Prompts] = Field(None, description="Prompt instructions at the top level")
