from fastapi import FastAP<PERSON>, responses
from fastapi.middleware.cors import CORSMiddleware
from app.v1.api import router as v1_api

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

app.mount("/v1", v1_api)

@app.get("/")
async def root():
    return responses.RedirectResponse(url="/v1/docs")

# if __name__ == "__main__":
#     import uvicorn
#     uvicorn.run(app, host="0.0.0.0", port=8000)

