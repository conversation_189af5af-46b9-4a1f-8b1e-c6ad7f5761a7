# Aroma Audio Transcription API - Documentation

## Overview

This document provides step-by-step guidance to authenticate, obtain pre-signed URLs for audio files, and invoke the `audio-transcribe-analysis` process using the Aroma API.

---

## Authentication

### Endpoint

```
POST https://aroma-api.nextai.asia/v1/login
```

### Headers

```
accept: application/json
Content-Type: application/x-www-form-urlencoded
```

### Body (form-urlencoded)

```
grant_type=password&username=demoagent_rl&password=strongpassword123&scope=&client_id=agsociar
```

### Sample `curl`

```bash
curl -X 'POST' \
  'https://aroma-api.nextai.asia/v1/login' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'grant_type=password&username=demoagent_rl&password=strongpassword123&scope=&client_id=agsociar'
```

### Sample Response

```json
{
  "id": "6819dc0517b016083729902b",
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkZW1vYWdlbnRfcmwiLCJyb2xlIjoiYWRtaW4iLCJ0ZW5hbnRfaWQiOiI2ODA0Y2JkZGE1MmM1Y2FiOTRjZjk3N2IiLCJleHAiOjE3NDY2MTE2MjN9.RsRNhjML-Qhx0YNAMek0XN8N_78oxW55skSBxD-hzl4",
  "token_type": "bearer",
  "username": "demoagent_rl",
  "role": "admin",
  "tenant_id": "6804cbdda52c5cab94cf977b",
  "tenant_label": "agsociar",
  "tenant_slug": "agsociar"
}
```

---

## Get Pre-Signed URL for MinIO Object

### Endpoint

```
GET https://aroma-api.nextai.asia/v1/jobs/media/presigned-url?object_name=<object_name>&expiry=3600
```

### Headers

```
accept: application/json
Authorization: Bearer <JWT_TOKEN>
```

### Sample `curl`

```bash
curl -X 'GET' \
  'https://aroma-api.nextai.asia/v1/jobs/media/presigned-url?object_name=6804ea1aee27345296fd00be%2Finput%2Frecording%2520%25281%2529.mp3&expiry=3600' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <JWT_TOKEN>'
```

### Sample Response

```json
{
  "presigned_url": "https://minio.nextai.asia/aroma.agsociar/6804ea1aee27345296fd00be/input/recording%2520%25281%2529.mp3?..."
}
```

Use this URL for the transcription process in the next step.

---

## Run Audio Transcription Process

### Endpoint

```
POST https://aroma-api.nextai.asia/v1/processes/audio-transcribe-analysis/run?wait_for_completion=true&timeout=300
```

### Headers

```
accept: application/json
Authorization: Bearer <JWT_TOKEN>
Content-Type: multipart/form-data
```

### Form Data

* `urls`: Pre-signed URL(s) or direct audio URL(s)

### Sample `curl`

```bash
curl -X 'POST' \
  'https://aroma-api.nextai.asia/v1/processes/audio-transcribe-analysis/run?wait_for_completion=true&timeout=300' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <JWT_TOKEN>' \
  -H 'Content-Type: multipart/form-data' \
  -F 'urls=https://minio.nextai.asia/aroma.agsociar/6804ea1aee27345296fd00be/input/recording%2520%25281%2529.mp3?...'
```

### Sample Response

```json
{ ... lengthy JSON output ... }
```

---

## Sample MinIO Object Names for Testing

Use the following object names to test the `presigned-url` generation and audio transcription process:

```
"6804ea1aee27345296fd00be/input/recording%20%281%29.mp3"
"68060e4b79b6f882b660741c/input/recording%2520%25281%2529.mp3"
"68074b01822af342e4595ffe/input/recording (3).mp3"
"680754d58a269c7eeaca667f/input/recording (2).mp3"
```

Make sure to URL-encode these object names when calling the `presigned-url` endpoint.

---

## Notes

* Replace `<JWT_TOKEN>` with the token received from the login call.
* You may use your own publicly accessible audio URLs in place of MinIO pre-signed URLs.
* Ensure audio files are in supported formats like `.mp3`, `.wav`, etc.
* You can invoke multiple files by repeating the `urls` field or submitting an array (depending on backend support).

---

## Troubleshooting

* Ensure token has not expired (default expiry is 1 hour).
* MinIO object names must be URL-encoded.
* Double-encoding may be required for special characters (e.g., spaces).
* Always use valid tenant-based tokens for permissioned operations.
