pipeline {
    agent any

    stages {
        stage('Setup') {
            steps {
                withCredentials([file(credentialsId: 'aroma-v2-backend-env', variable: 'ENV_FILE')]) {
                    sh 'cp "$ENV_FILE" .env'
                }
            }
        }

        stage('Build ') {
            steps {
                script {
                    try {
                        sh '''
                            docker compose build --no-cache
                        '''
                        echo "✅ Deployed successfully"
                    } catch (Exception err) {
                        currentBuild.result = 'FAILURE'
                        throw err
                    }
                }
            }
        }
    
      stage('Deploy') {
            steps {
                script {
                    try {
                        sh '''
                            docker compose down
                            docker compose up -d 
                        '''
                        echo "✅ Deployed successfully"
                    } catch (Exception err) {
                        currentBuild.result = 'FAILURE'
                        throw err
                    }
                }
            }
        }
    }

    post {
        always {
            sh 'docker system prune -f'
        }
    }
}